package com.tem.customer.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
import org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestClient;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * HTTP客户端配置
 * 提供统一的RestClient配置，支持各种HTTP请求场景
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Configuration
public class HttpClientConfig {

    /**
     * 通用RestClient配置
     * 提供统一的HTTP客户端，支持各种业务场景
     *
     * @param restClientBuilder Spring Boot提供的RestClient.Builder
     * @return RestClient实例
     */
    @Bean
    @Primary
    public RestClient restClient(RestClient.Builder restClientBuilder) {
        // 创建自定义的ClientHttpRequestFactory设置
        ClientHttpRequestFactorySettings settings = ClientHttpRequestFactorySettings.defaults()
                // 连接超时：10秒
                .withConnectTimeout(Duration.ofSeconds(10))
                // 读取超时：30秒
                .withReadTimeout(Duration.ofSeconds(30));

        // 自动检测并创建最佳的HTTP客户端工厂
        // 优先级：Apache HttpClient > Jetty HttpClient > Reactor Netty > JDK HttpClient > Simple JDK
        ClientHttpRequestFactory requestFactory = ClientHttpRequestFactoryBuilder.detect().build(settings);

        RestClient restClient = restClientBuilder
                .requestFactory(requestFactory)
                // 配置消息转换器
                .messageConverters(converters -> {
                    // 设置字符串转换器的默认编码为UTF-8
                    converters.stream()
                            .filter(converter -> converter instanceof StringHttpMessageConverter)
                            .forEach(converter -> ((StringHttpMessageConverter) converter)
                                    .setDefaultCharset(StandardCharsets.UTF_8));
                })
                // 配置默认请求头
                .defaultHeaders(headers -> {
                    headers.set("User-Agent", "Customer-Admin-Web/1.0");
                    headers.set("Accept-Charset", "UTF-8");
                })
                .build();

        log.info("RestClient配置完成 - 连接超时: 10s, 读取超时: 30s, 字符编码: UTF-8");
        log.info("HTTP客户端工厂类型: {}", requestFactory.getClass().getSimpleName());

        return restClient;
    }
}
