package com.tem.customer.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
import org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.web.client.RestClient;

import java.time.Duration;

/**
 * 企业微信RestClient配置
 * 专门为企业微信API调用优化的HTTP客户端配置
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Configuration
public class WechatRestClientConfig {

    /**
     * 企业微信专用RestClient
     * 针对企业微信API的特殊需求进行优化配置
     *
     * @param restClientBuilder Spring Boot提供的RestClient.Builder
     * @param wechatApiProperties 企业微信API配置属性
     * @return 企业微信专用RestClient实例
     */
    @Bean("wechatRestClient")
    public RestClient wechatRestClient(RestClient.Builder restClientBuilder, WechatApiProperties wechatApiProperties) {
        // 创建针对企业微信API的ClientHttpRequestFactory设置
        ClientHttpRequestFactorySettings settings = ClientHttpRequestFactorySettings.defaults()
                // 使用配置文件中的连接超时时间
                .withConnectTimeout(Duration.ofMillis(wechatApiProperties.getConnectTimeout()))
                // 使用配置文件中的读取超时时间
                .withReadTimeout(Duration.ofMillis(wechatApiProperties.getReadTimeout()));

        // 自动检测并创建最佳的HTTP客户端工厂
        ClientHttpRequestFactory requestFactory = ClientHttpRequestFactoryBuilder.detect().build(settings);

        RestClient restClient = restClientBuilder
                // 设置企业微信API基础URL
                .baseUrl(wechatApiProperties.getBaseUrl())
                .requestFactory(requestFactory)
                // 配置企业微信API专用的默认请求头
                .defaultHeaders(headers -> {
                    headers.set("User-Agent", "Customer-Admin-Web-Wechat/1.0");
                    headers.set("Accept", "application/json");
                    headers.set("Accept-Charset", "UTF-8");
                    headers.set("Content-Type", "application/json;charset=UTF-8");
                })
                .build();

        log.info("企业微信RestClient配置完成 - 基础URL: {}, 连接超时: {}ms, 读取超时: {}ms",
                wechatApiProperties.getBaseUrl(),
                wechatApiProperties.getConnectTimeout(),
                wechatApiProperties.getReadTimeout());
        log.info("企业微信HTTP客户端工厂类型: {}", requestFactory.getClass().getSimpleName());

        return restClient;
    }
}
